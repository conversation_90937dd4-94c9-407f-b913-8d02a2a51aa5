import { S3Client } from '@aws-sdk/client-s3';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { SQSClient } from '@aws-sdk/client-sqs';

const region = process.env.AWS_REGION || 'us-east-1';

// S3 Client
export const s3Client = new S3Client({
  region,
  maxAttempts: 3,
});

// DynamoDB Clients
const dynamoClient = new DynamoDBClient({
  region,
  maxAttempts: 3,
});

export const docClient = DynamoDBDocumentClient.from(dynamoClient, {
  marshallOptions: {
    convertEmptyValues: false,
    removeUndefinedValues: true,
    convertClassInstanceToMap: false,
  },
  unmarshallOptions: {
    wrapNumbers: false,
  },
});

// SQS Client
export const sqsClient = new SQSClient({
  region,
  maxAttempts: 3,
});

// SendGrid Configuration
export const SENDGRID_CONFIG = {
  API_KEY: process.env.SENDGRID_API_KEY!,
  FROM_EMAIL: process.env.FROM_EMAIL || '<EMAIL>',
};

// Environment variables
export const ENV = {
  UPLOAD_BUCKET: process.env.UPLOAD_BUCKET!,
  COMPRESSED_BUCKET: process.env.COMPRESSED_BUCKET!,
  DECOMPRESSED_BUCKET: process.env.DECOMPRESSED_BUCKET!,
  TRANSFER_TABLE: process.env.TRANSFER_TABLE!,
  JOB_TABLE: process.env.JOB_TABLE!,
  COMPRESSION_QUEUE_URL: process.env.COMPRESSION_QUEUE_URL!,
  DECOMPRESSION_QUEUE_URL: process.env.DECOMPRESSION_QUEUE_URL!,
  CLOUDFRONT_DOMAIN: process.env.CLOUDFRONT_DOMAIN!,
  BASE_URL: process.env.BASE_URL!,
  SENDGRID_API_KEY: process.env.SENDGRID_API_KEY!,
};
