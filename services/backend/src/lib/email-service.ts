import sgMail from '@sendgrid/mail';
import { SENDGRID_CONFIG } from './aws-clients';

export interface FileInfo {
  name: string;
  size: number;
  originalSize?: number;
}

export interface EmailShareRequest {
  transferId: string;
  recipientEmail: string;
  senderName?: string;
  shareLink: string;
  files: FileInfo[];
  totalSize: number;
  totalOriginalSize?: number;
  compressionRatio?: number;
  expiresAt: string;
  downloadLimit: number;
}

export interface EmailResponse {
  success: boolean;
  error?: string;
}

// Initialize SendGrid
sgMail.setApiKey(SENDGRID_CONFIG.API_KEY);

/**
 * Generate HTML email template for sharing download links
 */
function generateEmailTemplate(data: EmailShareRequest): string {
  const { files, totalSize, totalOriginalSize, compressionRatio, shareLink, expiresAt, downloadLimit, senderName } = data;
  
  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const senderText = senderName ? `${senderName} has` : 'Someone has';
  const fileCount = files.length;
  const fileText = fileCount === 1 ? 'file' : 'files';
  const expirationText = expiresAt ? `This link expires on ${formatDate(expiresAt)}.` : '';
  const downloadLimitText = downloadLimit ? `These ${fileText} can be downloaded ${downloadLimit} time${downloadLimit > 1 ? 's' : ''}.` : '';

  // Generate file list HTML
  const fileListHtml = files.map(file => `
    <div class="file-item">
      <div class="file-name">📁 ${file.name}</div>
      <div class="file-size">Size: ${formatFileSize(file.size)}</div>
    </div>
  `).join('');

  // Calculate compression info
  const compressionText = compressionRatio ?
    `<div class="compression-info">
      <strong>⚡ Compression:</strong> ${compressionRatio.toFixed(1)}% smaller than original
      ${totalOriginalSize ? `(${formatFileSize(totalOriginalSize)} → ${formatFileSize(totalSize)})` : ''}
    </div>` : '';

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Shared with You - FastTransfer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        .logo {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6, #ff69b4);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin: 0;
        }
        .subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 8px 0 0 0;
        }
        .file-info {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
        }
        .file-item {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            border-left: 4px solid #8b5cf6;
        }
        .file-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        .file-size {
            font-size: 14px;
            color: #6b7280;
        }
        .compression-info {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            font-size: 14px;
            color: #047857;
        }
        .total-info {
            background: #eff6ff;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            font-size: 14px;
            color: #1e40af;
            font-weight: 600;
        }
        .download-button {
            display: inline-block;
            background: linear-gradient(135deg, #4f46e5, #8b5cf6);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 24px 0;
            transition: transform 0.2s;
        }
        .download-button:hover {
            transform: translateY(-2px);
        }
        .info-text {
            font-size: 14px;
            color: #6b7280;
            margin: 16px 0;
        }
        .footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
            font-size: 12px;
            color: #9ca3af;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            font-size: 14px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">⚡ FastTransfer</div>
            <h1 class="title">${fileCount > 1 ? 'Files' : 'File'} Shared with You!</h1>
            <p class="subtitle">${senderText} shared ${fileCount} ${fileText} with you</p>
        </div>

        <div class="total-info">
            📦 Total: ${fileCount} ${fileText} (${formatFileSize(totalSize)})
        </div>

        ${compressionText}

        <div class="file-info">
            ${fileListHtml}
        </div>

        <div style="text-align: center;">
            <a href="${shareLink}" class="download-button">
                ⬇️ Download ${fileCount > 1 ? 'All Files' : 'File'}
            </a>
        </div>

        ${expirationText || downloadLimitText ? `
        <div class="warning">
            <strong>⚠️ Important:</strong><br>
            ${expirationText} ${downloadLimitText}
        </div>
        ` : ''}

        <div class="info-text">
            <p>This file was compressed using our advanced ZMT technology for faster transfers. Click the download button above to get your file.</p>
            <p>If you have any issues downloading the file, please contact the person who sent it to you.</p>
        </div>

        <div class="footer">
            <p>This email was sent by FastTransfer - Ultra-fast file compression and transfer platform</p>
            <p>If you didn't expect this email, you can safely ignore it.</p>
        </div>
    </div>
</body>
</html>
  `.trim();
}

/**
 * Generate plain text email template for sharing download links
 */
function generateTextTemplate(data: EmailShareRequest): string {
  const { files, totalSize, totalOriginalSize, compressionRatio, shareLink, expiresAt, downloadLimit, senderName } = data;
  
  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const senderText = senderName ? `${senderName} has` : 'Someone has';
  const fileCount = files.length;
  const fileText = fileCount === 1 ? 'file' : 'files';
  const expirationText = expiresAt ? `This link expires on ${formatDate(expiresAt)}.` : '';
  const downloadLimitText = downloadLimit ? `These ${fileText} can be downloaded ${downloadLimit} time${downloadLimit > 1 ? 's' : ''}.` : '';

  // Generate file list text
  const fileListText = files.map(file =>
    `  - ${file.name} (${formatFileSize(file.size)})`
  ).join('\n');

  const compressionText = compressionRatio ?
    `\nCompression: ${compressionRatio.toFixed(1)}% smaller than original${totalOriginalSize ? ` (${formatFileSize(totalOriginalSize)} → ${formatFileSize(totalSize)})` : ''}` : '';

  return `
FastTransfer - ${fileCount > 1 ? 'Files' : 'File'} Shared with You!

${senderText} shared ${fileCount} ${fileText} with you:

Total: ${fileCount} ${fileText} (${formatFileSize(totalSize)})${compressionText}

Files:
${fileListText}

Download Link: ${shareLink}

${expirationText}
${downloadLimitText}

This file was compressed using our advanced ZMT technology for faster transfers. Click the download link above to get your file.

If you have any issues downloading the file, please contact the person who sent it to you.

---
This email was sent by FastTransfer - Ultra-fast file compression and transfer platform
If you didn't expect this email, you can safely ignore it.
  `.trim();
}

/**
 * Send email with download link using SendGrid
 */
export async function sendShareEmail(request: EmailShareRequest): Promise<EmailResponse> {
  try {
    const { recipientEmail, files, senderName } = request;
    
    // Validate email address
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return {
        success: false,
        error: 'Invalid email address'
      };
    }

    const fileCount = files.length;
    const fileText = fileCount === 1 ? 'file' : 'files';
    const fileNames = files.length === 1 ? files[0].name : `${files.length} files`;

    const subject = senderName
      ? `${senderName} shared ${fileNames} with you via FastTransfer`
      : `${fileNames} shared with you via FastTransfer`;

    const htmlBody = generateEmailTemplate(request);
    const textBody = generateTextTemplate(request);

    const msg = {
      to: recipientEmail,
      from: SENDGRID_CONFIG.FROM_EMAIL,
      subject: subject,
      text: textBody,
      html: htmlBody,
    };

    await sgMail.send(msg);

    return {
      success: true
    };

  } catch (error) {
    console.error('Failed to send email via SendGrid:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
