import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { 
  createApiResponse, 
  createErrorResponse 
} from '../lib/utils';
import {
  createOrGetUser,
  getUserByEmail,
  completeUserAccount,
  validateUserUpload,
  DATA_LIMITS,
  formatBytes,
  generateVerificationToken,
  isValidEmail
} from '../lib/user-service';
import { 
  CreateUserRequest,
  CreateUserResponse,
  CompleteAccountRequest,
  CompleteAccountResponse,
  UserStatusResponse,
  UserStatus
} from '../types';

/**
 * Handle user creation (POST /user)
 */
export const createUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    if (!event.body) {
      return createErrorResponse(400, 'Request body is required');
    }

    const request: CreateUserRequest = JSON.parse(event.body);

    // Validate email
    if (!request.email || !isValidEmail(request.email)) {
      return createErrorResponse(400, 'Valid email address is required');
    }

    // Create or get existing user
    const result = await createOrGetUser(request.email);

    if (!result.isNew) {
      return createErrorResponse(409, 'Email already registered');
    }

    const response: CreateUserResponse = {
      email: result.user.email,
      status: result.user.status,
      dataUploaded: result.user.dataUploaded,
      message: 'Account created successfully. Check your email to complete registration.',
    };

    return createApiResponse(201, response);

  } catch (error) {
    console.error('Create user error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};

/**
 * Handle account completion (POST /user/complete)
 */
export const completeAccount = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    if (!event.body) {
      return createErrorResponse(400, 'Request body is required');
    }

    const request: CompleteAccountRequest = JSON.parse(event.body);

    // Validate required fields
    if (!request.email || !isValidEmail(request.email)) {
      return createErrorResponse(400, 'Valid email address is required');
    }

    if (!request.token) {
      return createErrorResponse(400, 'Verification token is required');
    }

    // TODO: Validate verification token (implement token validation logic)
    // For now, we'll skip token validation for simplicity

    // Complete the account
    const user = await completeUserAccount(request.email, request.name, request.password);

    const response: CompleteAccountResponse = {
      email: user.email,
      status: user.status,
      message: 'Account completed successfully. You now have 1TB of storage!',
    };

    return createApiResponse(200, response);

  } catch (error: any) {
    console.error('Complete account error:', error);
    
    if (error.message === 'User account is already active or not found') {
      return createErrorResponse(400, error.message);
    }

    return createErrorResponse(500, 'Internal server error', error);
  }
};

/**
 * Handle user status check (GET /user/{email}/status)
 */
export const getUserStatus = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'GET') {
      return createErrorResponse(405, 'Method not allowed');
    }

    const email = event.pathParameters?.email;
    if (!email || !isValidEmail(decodeURIComponent(email))) {
      return createErrorResponse(400, 'Valid email address is required');
    }

    const user = await getUserByEmail(decodeURIComponent(email));
    if (!user) {
      return createErrorResponse(404, 'User not found');
    }

    const dataLimit = user.status === UserStatus.PENDING 
      ? DATA_LIMITS.PENDING_USER_LIMIT 
      : DATA_LIMITS.ACTIVE_USER_LIMIT;

    const remainingBytes = Math.max(0, dataLimit - user.dataUploaded);
    const canUpload = remainingBytes > 0;

    const response: UserStatusResponse = {
      email: user.email,
      status: user.status,
      dataUploaded: user.dataUploaded,
      dataLimit,
      canUpload,
      upgradeRequired: user.status === UserStatus.PENDING && !canUpload,
    };

    return createApiResponse(200, response);

  } catch (error) {
    console.error('Get user status error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};

/**
 * Handle upload validation (POST /user/validate-upload)
 */
export const validateUpload = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return createApiResponse(200, {});
    }

    if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, 'Method not allowed');
    }

    if (!event.body) {
      return createErrorResponse(400, 'Request body is required');
    }

    const { email, uploadSize } = JSON.parse(event.body);

    if (!email || !isValidEmail(email)) {
      return createErrorResponse(400, 'Valid email address is required');
    }

    if (!uploadSize || uploadSize <= 0) {
      return createErrorResponse(400, 'Valid upload size is required');
    }

    const validation = await validateUserUpload(email, uploadSize);

    const response = {
      canUpload: validation.canUpload,
      remainingBytes: validation.remainingBytes,
      remainingFormatted: formatBytes(validation.remainingBytes),
      upgradeRequired: validation.upgradeRequired,
      message: validation.message,
    };

    return createApiResponse(200, response);

  } catch (error: any) {
    console.error('Validate upload error:', error);
    
    if (error.message === 'User not found') {
      return createErrorResponse(404, error.message);
    }

    return createErrorResponse(500, 'Internal server error', error);
  }
};

/**
 * Main handler that routes to appropriate function based on path
 */
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const path = event.resource || event.path;
  
  try {
    switch (path) {
      case '/user':
        return await createUser(event);
      case '/user/complete':
        return await completeAccount(event);
      case '/user/{email}/status':
        return await getUserStatus(event);
      case '/user/validate-upload':
        return await validateUpload(event);
      default:
        return createErrorResponse(404, 'Not found');
    }
  } catch (error) {
    console.error('User handler error:', error);
    return createErrorResponse(500, 'Internal server error', error);
  }
};
