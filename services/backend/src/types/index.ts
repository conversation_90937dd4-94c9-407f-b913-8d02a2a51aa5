export interface Transfer {
  transferId: string;
  originalFiles: FileInfo[];
  compressedSize?: number;
  originalSize: number;
  compressionRatio?: number;
  status: TransferStatus;
  createdAt: number;
  expiresAt: number;
  downloadLimit?: number;
  downloadCount: number;
  password?: string;
  email?: string;
}

export interface FileInfo {
  fileName: string;
  fileSize: number;
  contentType: string;
  s3Key: string;
}

export interface Job {
  jobId: string;
  transferId: string;
  type: JobType;
  status: JobStatus;
  createdAt: number;
  updatedAt: number;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export enum TransferStatus {
  UPLOADING = 'UPLOADING',
  UPLOADED = 'UPLOADED',
  COMPRESSING = 'COMPRESSING',
  COMPRESSED = 'COMPRESSED',
  READY = 'READY',
  DECOMPRESSING = 'DECOMPRESSING',
  ERROR = 'ERROR',
  EXPIRED = 'EXPIRED'
}

export enum JobType {
  COMPRESSION = 'COMPRESSION',
  DECOMPRESSION = 'DECOMPRESSION'
}

export enum JobStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export interface UploadRequest {
  files: {
    fileName: string;
    fileSize: number;
    contentType: string;
  }[];
  expirationHours?: number;
  downloadLimit?: number;
  password?: string;
  email?: string;
}

export interface UploadResponse {
  transferId: string;
  uploadUrls: {
    fileName: string;
    uploadUrl: string;
    s3Key: string;
  }[];
  expiresAt: number;
}

export interface LinkRequest {
  transferId: string;
  expirationHours?: number;
  downloadLimit?: number;
  password?: string;
}

export interface LinkResponse {
  transferId: string;
  shareUrl: string;
  expiresAt: number;
  downloadLimit?: number;
}

export interface DownloadRequest {
  transferId: string;
  password?: string;
}

export interface DownloadResponse {
  transferId: string;
  files: {
    fileName: string;
    downloadUrl: string;
    fileSize: number;
  }[];
  status: TransferStatus;
}
