import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import { Construct } from 'constructs';

export class FastTransferStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // S3 Buckets
    this.createS3Buckets();
    
    // DynamoDB Tables
    this.createDynamoDBTables();
    
    // SQS Queues
    this.createSQSQueues();
    
    // IAM Roles
    this.createIAMRoles();
    
    // VPC and EC2
    this.createVPCAndEC2();

    // CloudFront CDN
    this.createCloudFrontDistribution();
  }

  private createS3Buckets() {
    // Upload bucket for raw files
    const uploadBucket = new s3.Bucket(this, 'UploadBucket', {
      bucketName: `fasttransfer-upload-${this.account}-${this.region}`,
      versioned: false,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      lifecycleRules: [
        {
          id: 'DeleteUploadsAfter7Days',
          enabled: true,
          expiration: cdk.Duration.days(7),
        },
      ],
      transferAcceleration: true,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.GET,
            s3.HttpMethods.POST,
            s3.HttpMethods.PUT,
            s3.HttpMethods.DELETE,
          ],
          allowedOrigins: ['*'], // TODO: Restrict to actual domain
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
    });

    // Compressed bucket for .zmt files
    const compressedBucket = new s3.Bucket(this, 'CompressedBucket', {
      bucketName: `fasttransfer-compressed-${this.account}-${this.region}`,
      versioned: false,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      lifecycleRules: [
        {
          id: 'DeleteCompressedAfter7Days',
          enabled: true,
          expiration: cdk.Duration.days(7),
        },
      ],
    });

    // Decompressed bucket for serving files via CloudFront
    const decompressedBucket = new s3.Bucket(this, 'DecompressedBucket', {
      bucketName: `fasttransfer-decompressed-${this.account}-${this.region}`,
      versioned: false,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      lifecycleRules: [
        {
          id: 'DeleteDecompressedAfter7Days',
          enabled: true,
          expiration: cdk.Duration.days(7),
        },
      ],
    });

    // Export bucket names for use in other stacks
    new cdk.CfnOutput(this, 'UploadBucketName', {
      value: uploadBucket.bucketName,
      exportName: 'FastTransfer-UploadBucket',
    });

    new cdk.CfnOutput(this, 'CompressedBucketName', {
      value: compressedBucket.bucketName,
      exportName: 'FastTransfer-CompressedBucket',
    });

    new cdk.CfnOutput(this, 'DecompressedBucketName', {
      value: decompressedBucket.bucketName,
      exportName: 'FastTransfer-DecompressedBucket',
    });
  }

  private createDynamoDBTables() {
    // Transfer metadata table
    const transferTable = new dynamodb.Table(this, 'TransferTable', {
      tableName: 'FastTransfer-Transfers',
      partitionKey: {
        name: 'transferId',
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      pointInTimeRecovery: true,
      timeToLiveAttribute: 'expiresAt',
    });

    // Job tracking table
    const jobTable = new dynamodb.Table(this, 'JobTable', {
      tableName: 'FastTransfer-Jobs',
      partitionKey: {
        name: 'jobId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.NUMBER,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      pointInTimeRecovery: true,
    });

    // Add GSI for querying jobs by status
    jobTable.addGlobalSecondaryIndex({
      indexName: 'StatusIndex',
      partitionKey: {
        name: 'status',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.NUMBER,
      },
    });

    new cdk.CfnOutput(this, 'TransferTableName', {
      value: transferTable.tableName,
      exportName: 'FastTransfer-TransferTable',
    });

    new cdk.CfnOutput(this, 'JobTableName', {
      value: jobTable.tableName,
      exportName: 'FastTransfer-JobTable',
    });
  }

  private createSQSQueues() {
    // Dead letter queue for failed jobs
    const dlq = new sqs.Queue(this, 'JobDLQ', {
      queueName: 'FastTransfer-JobDLQ',
      encryption: sqs.QueueEncryption.SQS_MANAGED,
    });

    // Compression job queue
    const compressionQueue = new sqs.Queue(this, 'CompressionQueue', {
      queueName: 'FastTransfer-CompressionJobs',
      encryption: sqs.QueueEncryption.SQS_MANAGED,
      visibilityTimeout: cdk.Duration.minutes(15), // Allow time for compression
      deadLetterQueue: {
        queue: dlq,
        maxReceiveCount: 3,
      },
    });

    // Decompression job queue
    const decompressionQueue = new sqs.Queue(this, 'DecompressionQueue', {
      queueName: 'FastTransfer-DecompressionJobs',
      encryption: sqs.QueueEncryption.SQS_MANAGED,
      visibilityTimeout: cdk.Duration.minutes(10), // Allow time for decompression
      deadLetterQueue: {
        queue: dlq,
        maxReceiveCount: 3,
      },
    });

    new cdk.CfnOutput(this, 'CompressionQueueUrl', {
      value: compressionQueue.queueUrl,
      exportName: 'FastTransfer-CompressionQueue',
    });

    new cdk.CfnOutput(this, 'DecompressionQueueUrl', {
      value: decompressionQueue.queueUrl,
      exportName: 'FastTransfer-DecompressionQueue',
    });
  }

  private createIAMRoles() {
    // Lambda execution role
    const lambdaRole = new iam.Role(this, 'LambdaExecutionRole', {
      roleName: 'FastTransfer-LambdaExecutionRole',
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
      ],
      inlinePolicies: {
        S3Access: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                's3:GetObject',
                's3:PutObject',
                's3:DeleteObject',
                's3:ListBucket',
              ],
              resources: [
                `arn:aws:s3:::fasttransfer-*-${this.account}-${this.region}`,
                `arn:aws:s3:::fasttransfer-*-${this.account}-${this.region}/*`,
              ],
            }),
          ],
        }),
        DynamoDBAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'dynamodb:GetItem',
                'dynamodb:PutItem',
                'dynamodb:UpdateItem',
                'dynamodb:DeleteItem',
                'dynamodb:Query',
                'dynamodb:Scan',
              ],
              resources: [
                `arn:aws:dynamodb:${this.region}:${this.account}:table/FastTransfer-*`,
              ],
            }),
          ],
        }),
        SQSAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'sqs:SendMessage',
                'sqs:ReceiveMessage',
                'sqs:DeleteMessage',
                'sqs:GetQueueAttributes',
              ],
              resources: [
                `arn:aws:sqs:${this.region}:${this.account}:FastTransfer-*`,
              ],
            }),
          ],
        }),
      },
    });

    new cdk.CfnOutput(this, 'LambdaRoleArn', {
      value: lambdaRole.roleArn,
      exportName: 'FastTransfer-LambdaRole',
    });
  }

  private createVPCAndEC2() {
    // VPC for EC2 workers
    const vpc = new ec2.Vpc(this, 'FastTransferVPC', {
      maxAzs: 2,
      natGateways: 1, // Cost optimization - use 1 NAT gateway
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'Public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'Private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
      ],
    });

    // Security group for EC2 workers
    const workerSecurityGroup = new ec2.SecurityGroup(this, 'WorkerSecurityGroup', {
      vpc,
      description: 'Security group for FastTransfer EC2 workers',
      allowAllOutbound: true,
    });

    // EC2 role for workers
    const ec2Role = new iam.Role(this, 'EC2WorkerRole', {
      assumedBy: new iam.ServicePrincipal('ec2.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore'),
      ],
      inlinePolicies: {
        WorkerAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                's3:GetObject',
                's3:PutObject',
                's3:DeleteObject',
              ],
              resources: [
                `arn:aws:s3:::fasttransfer-*-${this.account}-${this.region}/*`,
              ],
            }),
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'sqs:ReceiveMessage',
                'sqs:DeleteMessage',
                'sqs:GetQueueAttributes',
              ],
              resources: [
                `arn:aws:sqs:${this.region}:${this.account}:FastTransfer-*`,
              ],
            }),
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'dynamodb:UpdateItem',
                'dynamodb:PutItem',
              ],
              resources: [
                `arn:aws:dynamodb:${this.region}:${this.account}:table/FastTransfer-*`,
              ],
            }),
          ],
        }),
      },
    });

    const instanceProfile = new iam.CfnInstanceProfile(this, 'EC2InstanceProfile', {
      roles: [ec2Role.roleName],
    });

    new cdk.CfnOutput(this, 'VPCId', {
      value: vpc.vpcId,
      exportName: 'FastTransfer-VPC',
    });

    new cdk.CfnOutput(this, 'WorkerSecurityGroupId', {
      value: workerSecurityGroup.securityGroupId,
      exportName: 'FastTransfer-WorkerSecurityGroup',
    });

    new cdk.CfnOutput(this, 'EC2RoleArn', {
      value: ec2Role.roleArn,
      exportName: 'FastTransfer-EC2Role',
    });

    // Launch template for EC2 workers
    const launchTemplate = new ec2.LaunchTemplate(this, 'WorkerLaunchTemplate', {
      launchTemplateName: 'FastTransfer-Worker',
      instanceType: ec2.InstanceType.of(ec2.InstanceClass.C5, ec2.InstanceSize.LARGE),
      machineImage: ec2.MachineImage.latestAmazonLinux2({
        edition: ec2.AmazonLinuxEdition.STANDARD,
        virtualization: ec2.AmazonLinuxVirt.HVM,
        storage: ec2.AmazonLinuxStorage.GENERAL_PURPOSE,
      }),
      securityGroup: workerSecurityGroup,
      role: ec2Role,
      userData: ec2.UserData.forLinux(),
      blockDevices: [
        {
          deviceName: '/dev/xvda',
          volume: ec2.BlockDeviceVolume.ebs(20, {
            volumeType: ec2.EbsDeviceVolumeType.GP3,
            encrypted: true,
          }),
        },
      ],
    });

    // Add user data script for ZMT setup
    launchTemplate.userData.addCommands(
      '#!/bin/bash',
      'yum update -y',
      'yum install -y nodejs npm python3 python3-pip',

      // Install AWS CLI v2
      'curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"',
      'unzip awscliv2.zip',
      'sudo ./aws/install',

      // Create application directory
      'mkdir -p /opt/fasttransfer',
      'cd /opt/fasttransfer',

      // Download and setup ZMT (placeholder - actual ZMT binary would be downloaded here)
      'echo "#!/bin/bash" > /opt/fasttransfer/zmt',
      'echo "# ZMT compression tool placeholder" >> /opt/fasttransfer/zmt',
      'echo "# Usage: ./zmt [compress|extract] input output" >> /opt/fasttransfer/zmt',
      'chmod +x /opt/fasttransfer/zmt',

      // Install Node.js dependencies for worker service
      'cat > package.json << EOF',
      '{',
      '  "name": "fasttransfer-worker",',
      '  "version": "1.0.0",',
      '  "main": "worker.js",',
      '  "dependencies": {',
      '    "aws-sdk": "^2.1400.0",',
      '    "@aws-sdk/client-s3": "^3.400.0",',
      '    "@aws-sdk/client-sqs": "^3.400.0",',
      '    "@aws-sdk/client-dynamodb": "^3.400.0"',
      '  }',
      '}',
      'EOF',

      'npm install',

      // Create systemd service
      'cat > /etc/systemd/system/fasttransfer-worker.service << EOF',
      '[Unit]',
      'Description=FastTransfer Worker Service',
      'After=network.target',
      '',
      '[Service]',
      'Type=simple',
      'User=ec2-user',
      'WorkingDirectory=/opt/fasttransfer',
      'ExecStart=/usr/bin/node worker.js',
      'Restart=always',
      'RestartSec=10',
      'Environment=NODE_ENV=production',
      `Environment=AWS_REGION=${this.region}`,
      '',
      '[Install]',
      'WantedBy=multi-user.target',
      'EOF',

      // Enable and start the service
      'systemctl daemon-reload',
      'systemctl enable fasttransfer-worker',
      'systemctl start fasttransfer-worker',

      // Signal CloudFormation that the instance is ready
      '/opt/aws/bin/cfn-signal -e $? --stack ${AWS::StackName} --resource AutoScalingGroup --region ${AWS::Region}'
    );

    new cdk.CfnOutput(this, 'LaunchTemplateId', {
      value: launchTemplate.launchTemplateId!,
      exportName: 'FastTransfer-LaunchTemplate',
    });
  }

  private createCloudFrontDistribution() {
    // S3 bucket for static website hosting (frontend assets)
    const staticWebsiteBucket = new s3.Bucket(this, 'StaticWebsiteBucket', {
      bucketName: `fasttransfer-static-${this.account}-${this.region}`,
      versioned: false,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      lifecycleRules: [
        {
          id: 'DeleteOldVersions',
          enabled: true,
          noncurrentVersionExpiration: cdk.Duration.days(30),
        },
      ],
    });

    // Origin Access Control for CloudFront to access S3
    const originAccessControl = new cloudfront.CfnOriginAccessControl(this, 'OAC', {
      originAccessControlConfig: {
        name: 'FastTransfer-OAC',
        originAccessControlOriginType: 's3',
        signingBehavior: 'always',
        signingProtocol: 'sigv4',
        description: 'Origin Access Control for FastTransfer static assets',
      },
    });

    // CloudFront distribution
    const distribution = new cloudfront.Distribution(this, 'CDNDistribution', {
      comment: 'FastTransfer CDN for static assets and API',
      defaultRootObject: 'index.html',

      // Price class for cost optimization
      priceClass: cloudfront.PriceClass.PRICE_CLASS_100, // US, Canada, Europe

      // Enable HTTP/2 and HTTP/3
      httpVersion: cloudfront.HttpVersion.HTTP2_AND_3,

      // Default behavior for static assets (S3)
      defaultBehavior: {
        origin: origins.S3BucketOrigin.withOriginAccessControl(staticWebsiteBucket, {
          originAccessControl,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        compress: true,

        // Caching policy for static assets
        cachePolicy: new cloudfront.CachePolicy(this, 'StaticAssetsCachePolicy', {
          cachePolicyName: 'FastTransfer-StaticAssets',
          comment: 'Cache policy for static assets with long TTL',
          defaultTtl: cdk.Duration.days(30),
          maxTtl: cdk.Duration.days(365),
          minTtl: cdk.Duration.seconds(0),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
            'CloudFront-Viewer-Country',
            'CloudFront-Is-Mobile-Viewer',
            'CloudFront-Is-Tablet-Viewer',
            'CloudFront-Is-Desktop-Viewer'
          ),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          enableAcceptEncodingGzip: true,
          enableAcceptEncodingBrotli: true,
        }),
      },

      // Additional behaviors
      additionalBehaviors: {
        // API routes - no caching, forward all headers
        '/api/*': {
          origin: new origins.HttpOrigin(`api.${this.region}.amazonaws.com`, {
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTPS_ONLY,
            customHeaders: {
              'X-Forwarded-Host': '${aws:cloudfront:distribution-domain-name}',
            },
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: true,

          // No caching for API routes
          cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
          originRequestPolicy: cloudfront.OriginRequestPolicy.ALL_VIEWER_EXCEPT_HOST_HEADER,
        },

        // Static assets with versioned filenames - aggressive caching
        '/assets/*': {
          origin: origins.S3BucketOrigin.withOriginAccessControl(staticWebsiteBucket, {
            originAccessControl,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: true,

          // Long-term caching for versioned assets
          cachePolicy: new cloudfront.CachePolicy(this, 'VersionedAssetsCachePolicy', {
            cachePolicyName: 'FastTransfer-VersionedAssets',
            comment: 'Aggressive caching for versioned static assets',
            defaultTtl: cdk.Duration.days(365),
            maxTtl: cdk.Duration.days(365),
            minTtl: cdk.Duration.days(365),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
            headerBehavior: cloudfront.CacheHeaderBehavior.none(),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            enableAcceptEncodingGzip: true,
            enableAcceptEncodingBrotli: true,
          }),
        },

        // ZMT compressed files - medium-term caching with range request support
        '*.zmt': {
          origin: origins.S3BucketOrigin.withOriginAccessControl(staticWebsiteBucket, {
            originAccessControl,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false, // ZMT files are already compressed

          cachePolicy: new cloudfront.CachePolicy(this, 'ZMTFilesCachePolicy', {
            cachePolicyName: 'FastTransfer-ZMTFiles',
            comment: 'Medium-term caching for ZMT compressed files',
            defaultTtl: cdk.Duration.days(7),
            maxTtl: cdk.Duration.days(30),
            minTtl: cdk.Duration.minutes(5),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList('Range', 'If-Range'),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            enableAcceptEncodingGzip: false, // Already compressed
            enableAcceptEncodingBrotli: false,
          }),
        },

        // ZIP and archive files - medium-term caching
        '*.zip': {
          origin: origins.S3BucketOrigin.withOriginAccessControl(staticWebsiteBucket, {
            originAccessControl,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false, // ZIP files are already compressed

          cachePolicy: new cloudfront.CachePolicy(this, 'ArchiveFilesCachePolicy', {
            cachePolicyName: 'FastTransfer-ArchiveFiles',
            comment: 'Medium-term caching for archive files (ZIP, RAR, 7Z)',
            defaultTtl: cdk.Duration.days(7),
            maxTtl: cdk.Duration.days(30),
            minTtl: cdk.Duration.minutes(5),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList('Range', 'If-Range'),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            enableAcceptEncodingGzip: false,
            enableAcceptEncodingBrotli: false,
          }),
        },

        // HTML files - short-term caching for frequent updates
        '*.html': {
          origin: origins.S3BucketOrigin.withOriginAccessControl(staticWebsiteBucket, {
            originAccessControl,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: true,

          cachePolicy: new cloudfront.CachePolicy(this, 'HTMLFilesCachePolicy', {
            cachePolicyName: 'FastTransfer-HTMLFiles',
            comment: 'Short-term caching for HTML files with revalidation',
            defaultTtl: cdk.Duration.minutes(5),
            maxTtl: cdk.Duration.hours(1),
            minTtl: cdk.Duration.seconds(0),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList('Cache-Control'),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            enableAcceptEncodingGzip: true,
            enableAcceptEncodingBrotli: true,
          }),
        },

        // Document files - medium caching
        '*.pdf': {
          origin: origins.S3BucketOrigin.withOriginAccessControl(staticWebsiteBucket, {
            originAccessControl,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: true,

          cachePolicy: new cloudfront.CachePolicy(this, 'DocumentFilesCachePolicy', {
            cachePolicyName: 'FastTransfer-DocumentFiles',
            comment: 'Medium-term caching for document files',
            defaultTtl: cdk.Duration.days(1),
            maxTtl: cdk.Duration.days(7),
            minTtl: cdk.Duration.minutes(5),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList('Range', 'If-Range'),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            enableAcceptEncodingGzip: true,
            enableAcceptEncodingBrotli: true,
          }),
        },

        // Media files - long-term caching with range support
        '*.mp4': {
          origin: origins.S3BucketOrigin.withOriginAccessControl(staticWebsiteBucket, {
            originAccessControl,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
          compress: false, // Media files shouldn't be compressed

          cachePolicy: new cloudfront.CachePolicy(this, 'MediaFilesCachePolicy', {
            cachePolicyName: 'FastTransfer-MediaFiles',
            comment: 'Long-term caching for media files with range request support',
            defaultTtl: cdk.Duration.days(30),
            maxTtl: cdk.Duration.days(365),
            minTtl: cdk.Duration.hours(1),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList('Range', 'If-Range', 'Accept-Ranges'),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            enableAcceptEncodingGzip: false,
            enableAcceptEncodingBrotli: false,
          }),
        },
      },

      // Error pages
      errorResponses: [
        {
          httpStatus: 404,
          responseHttpStatus: 200,
          responsePagePath: '/index.html',
          ttl: cdk.Duration.minutes(5),
        },
        {
          httpStatus: 403,
          responseHttpStatus: 200,
          responsePagePath: '/index.html',
          ttl: cdk.Duration.minutes(5),
        },
      ],

      // Security headers
      responseHeadersPolicy: new cloudfront.ResponseHeadersPolicy(this, 'SecurityHeaders', {
        responseHeadersPolicyName: 'FastTransfer-SecurityHeaders',
        comment: 'Security headers for FastTransfer',
        securityHeadersBehavior: {
          contentTypeOptions: { override: true },
          frameOptions: { frameOption: cloudfront.HeadersFrameOption.DENY, override: true },
          referrerPolicy: { referrerPolicy: cloudfront.HeadersReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN, override: true },
          strictTransportSecurity: {
            accessControlMaxAge: cdk.Duration.seconds(31536000),
            includeSubdomains: true,
            preload: true,
            override: true,
          },
          contentSecurityPolicy: {
            contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self';",
            override: true,
          },
        },
        customHeadersBehavior: {
          'X-Content-Type-Options': { header: 'nosniff', value: '', override: true },
          'X-Frame-Options': { header: 'DENY', value: '', override: true },
          'Permissions-Policy': {
            header: 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
            value: '',
            override: true
          },
        },
      }),
    });

    // Update S3 bucket policy to allow CloudFront access
    staticWebsiteBucket.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        principals: [new iam.ServicePrincipal('cloudfront.amazonaws.com')],
        actions: ['s3:GetObject'],
        resources: [`${staticWebsiteBucket.bucketArn}/*`],
        conditions: {
          StringEquals: {
            'AWS:SourceArn': `arn:aws:cloudfront::${this.account}:distribution/${distribution.distributionId}`,
          },
        },
      })
    );

    // Outputs
    new cdk.CfnOutput(this, 'StaticWebsiteBucketName', {
      value: staticWebsiteBucket.bucketName,
      exportName: 'FastTransfer-StaticWebsiteBucket',
    });

    new cdk.CfnOutput(this, 'CloudFrontDistributionId', {
      value: distribution.distributionId,
      exportName: 'FastTransfer-CloudFrontDistributionId',
    });

    new cdk.CfnOutput(this, 'CloudFrontDomainName', {
      value: distribution.distributionDomainName,
      exportName: 'FastTransfer-CloudFrontDomain',
    });

    new cdk.CfnOutput(this, 'CloudFrontDistributionUrl', {
      value: `https://${distribution.distributionDomainName}`,
      exportName: 'FastTransfer-CloudFrontUrl',
    });
  }
}
